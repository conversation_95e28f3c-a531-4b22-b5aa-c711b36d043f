<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working Y.js Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .client {
            flex: 1;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 8px;
        }
        .editor {
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            border-radius: 4px;
        }
        .status {
            padding: 8px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            height: 100px;
            overflow-y: scroll;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>Working Y.js Test</h1>
    <p>Testing Y.js with local bundle - Real-time collaborative editing</p>
    
    <div class="container">
        <div class="client">
            <h3>Client 1</h3>
            <div id="status1" class="status disconnected">Disconnected</div>
            <button id="connect1">Connect Client 1</button>
            <button id="disconnect1" disabled>Disconnect Client 1</button>
            <textarea id="editor1" class="editor" placeholder="Type here and see it appear in Client 2..."></textarea>
            <div class="log" id="log1"></div>
        </div>
        
        <div class="client">
            <h3>Client 2</h3>
            <div id="status2" class="status disconnected">Disconnected</div>
            <button id="connect2">Connect Client 2</button>
            <button id="disconnect2" disabled>Disconnect Client 2</button>
            <textarea id="editor2" class="editor" placeholder="Type here and see it appear in Client 1..."></textarea>
            <div class="log" id="log2"></div>
        </div>
    </div>

    <!-- Use our local bundle -->
    <script src="./yjs-bundle.js"></script>
    
    <script>
        // Check if Y.js is loaded
        if (typeof Y === 'undefined') {
            document.body.innerHTML = '<h1>Error: Y.js library failed to load</h1><p>Bundle not found or failed to load.</p>';
            throw new Error('Y.js library not loaded');
        }
        
        if (typeof WebsocketProvider === 'undefined') {
            document.body.innerHTML = '<h1>Error: WebsocketProvider failed to load</h1><p>Bundle not found or failed to load.</p>';
            throw new Error('WebsocketProvider not loaded');
        }
        
        console.log('✅ Y.js loaded successfully:', Y);
        console.log('✅ WebsocketProvider loaded successfully:', WebsocketProvider);
        
        // Create two Y.js documents
        const doc1 = new Y.Doc();
        const doc2 = new Y.Doc();
        
        // Get text types
        const text1 = doc1.getText('content');
        const text2 = doc2.getText('content');
        
        // WebSocket providers (will be created on connect)
        let provider1 = null;
        let provider2 = null;
        
        // Get DOM elements
        const editor1 = document.getElementById('editor1');
        const editor2 = document.getElementById('editor2');
        const status1 = document.getElementById('status1');
        const status2 = document.getElementById('status2');
        const log1 = document.getElementById('log1');
        const log2 = document.getElementById('log2');
        const connect1Btn = document.getElementById('connect1');
        const disconnect1Btn = document.getElementById('disconnect1');
        const connect2Btn = document.getElementById('connect2');
        const disconnect2Btn = document.getElementById('disconnect2');
        
        let updating1 = false;
        let updating2 = false;
        
        function log(logEl, message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[${logEl.id}] ${message}`);
        }
        
        // Connect functions
        function connectClient1() {
            log(log1, '🔄 Connecting Client 1...');
            status1.textContent = 'Connecting...';
            status1.className = 'status connecting';
            
            try {
                provider1 = new WebsocketProvider('ws://localhost:3001', 'test-room', doc1);
                
                provider1.on('status', event => {
                    log(log1, `📡 Provider 1 status: ${event.status}`);
                    status1.textContent = event.status;
                    status1.className = `status ${event.status === 'connected' ? 'connected' : 'disconnected'}`;
                    
                    if (event.status === 'connected') {
                        connect1Btn.disabled = true;
                        disconnect1Btn.disabled = false;
                        log(log1, '✅ Client 1 connected successfully!');
                    } else if (event.status === 'disconnected') {
                        connect1Btn.disabled = false;
                        disconnect1Btn.disabled = true;
                    }
                });
                
                provider1.on('connection-error', error => {
                    log(log1, `❌ Provider 1 connection error: ${error}`);
                });
                
                provider1.on('connection-close', event => {
                    log(log1, `🔌 Provider 1 connection closed: ${event.code} ${event.reason}`);
                });
                
            } catch (error) {
                log(log1, `❌ Error creating Provider 1: ${error.message}`);
                status1.textContent = 'Error';
                status1.className = 'status disconnected';
            }
        }
        
        function connectClient2() {
            log(log2, '🔄 Connecting Client 2...');
            status2.textContent = 'Connecting...';
            status2.className = 'status connecting';
            
            try {
                provider2 = new WebsocketProvider('ws://localhost:3001', 'test-room', doc2);
                
                provider2.on('status', event => {
                    log(log2, `📡 Provider 2 status: ${event.status}`);
                    status2.textContent = event.status;
                    status2.className = `status ${event.status === 'connected' ? 'connected' : 'disconnected'}`;
                    
                    if (event.status === 'connected') {
                        connect2Btn.disabled = true;
                        disconnect2Btn.disabled = false;
                        log(log2, '✅ Client 2 connected successfully!');
                    } else if (event.status === 'disconnected') {
                        connect2Btn.disabled = false;
                        disconnect2Btn.disabled = true;
                    }
                });
                
                provider2.on('connection-error', error => {
                    log(log2, `❌ Provider 2 connection error: ${error}`);
                });
                
                provider2.on('connection-close', event => {
                    log(log2, `🔌 Provider 2 connection closed: ${event.code} ${event.reason}`);
                });
                
            } catch (error) {
                log(log2, `❌ Error creating Provider 2: ${error.message}`);
                status2.textContent = 'Error';
                status2.className = 'status disconnected';
            }
        }
        
        function disconnectClient1() {
            if (provider1) {
                provider1.destroy();
                provider1 = null;
                status1.textContent = 'Disconnected';
                status1.className = 'status disconnected';
                connect1Btn.disabled = false;
                disconnect1Btn.disabled = true;
                log(log1, '🔌 Client 1 disconnected');
            }
        }
        
        function disconnectClient2() {
            if (provider2) {
                provider2.destroy();
                provider2 = null;
                status2.textContent = 'Disconnected';
                status2.className = 'status disconnected';
                connect2Btn.disabled = false;
                disconnect2Btn.disabled = true;
                log(log2, '🔌 Client 2 disconnected');
            }
        }
        
        // Button event listeners
        connect1Btn.addEventListener('click', connectClient1);
        disconnect1Btn.addEventListener('click', disconnectClient1);
        connect2Btn.addEventListener('click', connectClient2);
        disconnect2Btn.addEventListener('click', disconnectClient2);
        
        // Observe text changes for real-time sync
        text1.observe(() => {
            if (!updating1) {
                updating2 = true;
                const newContent = text1.toString();
                if (editor2.value !== newContent) {
                    editor2.value = newContent;
                    log(log1, `📝 Text synced to Client 2: "${newContent}"`);
                }
                setTimeout(() => { updating2 = false; }, 10);
            }
        });

        text2.observe(() => {
            if (!updating2) {
                updating1 = true;
                const newContent = text2.toString();
                if (editor1.value !== newContent) {
                    editor1.value = newContent;
                    log(log2, `📝 Text synced to Client 1: "${newContent}"`);
                }
                setTimeout(() => { updating1 = false; }, 10);
            }
        });
        
        // Handle editor input
        editor1.addEventListener('input', () => {
            if (!updating1) {
                updating1 = true;
                const content = editor1.value;
                doc1.transact(() => {
                    text1.delete(0, text1.length);
                    text1.insert(0, content);
                });
                log(log1, `⌨️ Editor 1 input: "${content}"`);
                setTimeout(() => { updating1 = false; }, 10);
            }
        });

        editor2.addEventListener('input', () => {
            if (!updating2) {
                updating2 = true;
                const content = editor2.value;
                doc2.transact(() => {
                    text2.delete(0, text2.length);
                    text2.insert(0, content);
                });
                log(log2, `⌨️ Editor 2 input: "${content}"`);
                setTimeout(() => { updating2 = false; }, 10);
            }
        });
        
        log(log1, '🚀 Client 1 initialized and ready');
        log(log2, '🚀 Client 2 initialized and ready');
    </script>
</body>
</html>
