const http = require('http');
const WebSocket = require('ws');
const express = require('express');

// Import y-websocket server utilities
const { setupWSConnection } = require('@y/websocket-server/utils');

const app = express();
const server = http.createServer(app);

// Serve static files
app.use(express.static('.'));

// Create WebSocket server
const wss = new WebSocket.Server({ noServer: true });

wss.on('connection', (ws, req) => {
  console.log('New WebSocket connection:', req.url);
  setupWSConnection(ws, req);
});

// Handle WebSocket upgrade
server.on('upgrade', (request, socket, head) => {
  wss.handleUpgrade(request, socket, head, (ws) => {
    wss.emit('connection', ws, request);
  });
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`Minimal Y.js WebSocket server running on port ${PORT}`);
});
