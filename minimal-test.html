<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Y-WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .client {
            flex: 1;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 8px;
        }
        .editor {
            width: 100%;
            height: 150px;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            border-radius: 4px;
        }
        .status {
            padding: 8px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Minimal Y-WebSocket Test</h1>
    <p>Testing with minimal server setup</p>
    
    <div class="container">
        <div class="client">
            <h3>Client 1</h3>
            <div id="status1" class="status disconnected">Disconnected</div>
            <button id="connect1">Connect Client 1</button>
            <button id="disconnect1" disabled>Disconnect Client 1</button>
            <textarea id="editor1" class="editor" placeholder="Type here..."></textarea>
        </div>

        <div class="client">
            <h3>Client 2</h3>
            <div id="status2" class="status disconnected">Disconnected</div>
            <button id="connect2">Connect Client 2</button>
            <button id="disconnect2" disabled>Disconnect Client 2</button>
            <textarea id="editor2" class="editor" placeholder="Type here..."></textarea>
        </div>
    </div>

    <script src="https://unpkg.com/yjs@13.6.27/dist/yjs.js"></script>
    <script src="https://unpkg.com/y-websocket@3.0.0/dist/y-websocket.js"></script>
    
    <script>
        // Create two Y.js documents
        const doc1 = new Y.Doc();
        const doc2 = new Y.Doc();

        // Get text types
        const text1 = doc1.getText('content');
        const text2 = doc2.getText('content');

        // WebSocket providers (will be created on connect)
        let provider1 = null;
        let provider2 = null;
        
        // Get DOM elements
        const editor1 = document.getElementById('editor1');
        const editor2 = document.getElementById('editor2');
        const status1 = document.getElementById('status1');
        const status2 = document.getElementById('status2');
        const connect1Btn = document.getElementById('connect1');
        const disconnect1Btn = document.getElementById('disconnect1');
        const connect2Btn = document.getElementById('connect2');
        const disconnect2Btn = document.getElementById('disconnect2');
        
        let updating1 = false;
        let updating2 = false;

        // Connect functions
        function connectClient1() {
            console.log('Connecting Client 1...');
            try {
                provider1 = new WebsocketProvider('ws://localhost:3001', 'test-room', doc1);

                provider1.on('status', event => {
                    console.log('Provider 1 status:', event.status);
                    status1.textContent = event.status;
                    status1.className = `status ${event.status === 'connected' ? 'connected' : 'disconnected'}`;

                    if (event.status === 'connected') {
                        connect1Btn.disabled = true;
                        disconnect1Btn.disabled = false;
                    } else {
                        connect1Btn.disabled = false;
                        disconnect1Btn.disabled = true;
                    }
                });

                provider1.on('connection-error', error => {
                    console.error('Provider 1 connection error:', error);
                });

            } catch (error) {
                console.error('Error creating Provider 1:', error);
            }
        }

        function connectClient2() {
            console.log('Connecting Client 2...');
            try {
                provider2 = new WebsocketProvider('ws://localhost:3001', 'test-room', doc2);

                provider2.on('status', event => {
                    console.log('Provider 2 status:', event.status);
                    status2.textContent = event.status;
                    status2.className = `status ${event.status === 'connected' ? 'connected' : 'disconnected'}`;

                    if (event.status === 'connected') {
                        connect2Btn.disabled = true;
                        disconnect2Btn.disabled = false;
                    } else {
                        connect2Btn.disabled = false;
                        disconnect2Btn.disabled = true;
                    }
                });

                provider2.on('connection-error', error => {
                    console.error('Provider 2 connection error:', error);
                });

            } catch (error) {
                console.error('Error creating Provider 2:', error);
            }
        }

        function disconnectClient1() {
            if (provider1) {
                provider1.destroy();
                provider1 = null;
                status1.textContent = 'Disconnected';
                status1.className = 'status disconnected';
                connect1Btn.disabled = false;
                disconnect1Btn.disabled = true;
            }
        }

        function disconnectClient2() {
            if (provider2) {
                provider2.destroy();
                provider2 = null;
                status2.textContent = 'Disconnected';
                status2.className = 'status disconnected';
                connect2Btn.disabled = false;
                disconnect2Btn.disabled = true;
            }
        }

        // Button event listeners
        connect1Btn.addEventListener('click', connectClient1);
        disconnect1Btn.addEventListener('click', disconnectClient1);
        connect2Btn.addEventListener('click', connectClient2);
        disconnect2Btn.addEventListener('click', disconnectClient2);
        
        // Observe text changes
        text1.observe(() => {
            if (!updating1) {
                updating2 = true;
                editor2.value = text1.toString();
                console.log('Text 1 changed:', text1.toString());
                setTimeout(() => { updating2 = false; }, 0);
            }
        });
        
        text2.observe(() => {
            if (!updating2) {
                updating1 = true;
                editor1.value = text2.toString();
                console.log('Text 2 changed:', text2.toString());
                setTimeout(() => { updating1 = false; }, 0);
            }
        });
        
        // Handle editor input
        editor1.addEventListener('input', () => {
            if (!updating1) {
                updating1 = true;
                const content = editor1.value;
                doc1.transact(() => {
                    text1.delete(0, text1.length);
                    text1.insert(0, content);
                });
                console.log('Editor 1 input:', content);
                setTimeout(() => { updating1 = false; }, 0);
            }
        });
        
        editor2.addEventListener('input', () => {
            if (!updating2) {
                updating2 = true;
                const content = editor2.value;
                doc2.transact(() => {
                    text2.delete(0, text2.length);
                    text2.insert(0, content);
                });
                console.log('Editor 2 input:', content);
                setTimeout(() => { updating2 = false; }, 0);
            }
        });
    </script>
</body>
</html>
